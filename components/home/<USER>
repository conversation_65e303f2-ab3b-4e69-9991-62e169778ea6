import { <PERSON>, CardBody, CardHeader } from "@heroui/card";
import { Button } from "@heroui/button";
import { motion, usePresenceData, useIsPresent } from "framer-motion";
import { title } from "@/components/primitives";
import { contentVariants, cardItemVariants } from "@/utils/animationVariants";

interface HomeCardsLayoutProps {
  className?: string;
}

export const HomeCardsLayout: React.FC<HomeCardsLayoutProps> = ({
  className
}) => {
  const direction = usePresenceData();
  const isPresent = useIsPresent();

  return (
    <motion.section
      className={`flex flex-col gap-6 py-8 md:py-10 ${className || ""}`}
      variants={contentVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div
        className="text-center mb-8"
        variants={cardItemVariants}
      >
        <h1 className={title()}>欢迎来到首页</h1>
        <p className="text-default-600 mt-4">这里是首页的内容展示区域</p>
      </motion.div>

      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 gap-6"
        variants={contentVariants}
      >
        {/* 卡片1 */}
        <motion.div variants={cardItemVariants}>
          <Card className="p-4">
            <CardHeader className="pb-0 pt-2 px-4 flex-col items-start">
              <h4 className="font-bold text-large">功能卡片 1</h4>
              <small className="text-default-500">首页功能介绍</small>
            </CardHeader>
            <CardBody className="overflow-visible py-2">
              <p className="text-default-600 mb-4">
                这是首页的第一个功能卡片，展示相关内容和操作。
              </p>
              <Button color="primary" variant="flat">
                了解更多
              </Button>
            </CardBody>
          </Card>
        </motion.div>

        {/* 卡片2 */}
        <motion.div variants={cardItemVariants}>
          <Card className="p-4">
            <CardHeader className="pb-0 pt-2 px-4 flex-col items-start">
              <h4 className="font-bold text-large">功能卡片 2</h4>
              <small className="text-default-500">首页功能介绍</small>
            </CardHeader>
            <CardBody className="overflow-visible py-2">
              <p className="text-default-600 mb-4">
                这是首页的第二个功能卡片，展示相关内容和操作。
              </p>
              <Button color="secondary" variant="flat">
                开始使用
              </Button>
            </CardBody>
          </Card>
        </motion.div>
      </motion.div>
    </motion.section>
  );
};
